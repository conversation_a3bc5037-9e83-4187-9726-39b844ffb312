<?php
/**
 * Admin Customer View
 * Redolence Medi Aesthetics - Advanced Medical Beauty Management System
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || !hasRole('ADMIN')) {
    redirect('/admin/login');
}

// Get customer ID from URL
$customerId = $_GET['id'] ?? '';
if (empty($customerId)) {
    $_SESSION['error'] = 'Customer ID is required';
    redirect('/admin/customers');
}

// Try to get customer details
$customerData = getCustomerDetails($customerId);

if (!$customerData) {
    // Fallback: Try to get basic customer info directly
    $customer = $database->fetch(
        "SELECT * FROM users WHERE id = ? AND role = 'CUSTOMER'",
        [$customerId]
    );

    if (!$customer) {
        $_SESSION['error'] = 'Customer not found (ID: ' . htmlspecialchars($customerId) . ')';
        redirect('/admin/customers');
    }

    // Create minimal customer data structure
    $customerData = [
        'customer' => $customer,
        'stats' => [
            'total_bookings' => 0,
            'completed_bookings' => 0,
            'cancelled_bookings' => 0,
            'total_spent' => 0,
            'last_booking_date' => null,
            'first_booking_date' => null
        ],
        'recent_bookings' => [],
        'points_history' => [],
        'favorite_services' => []
    ];

    // Try to get basic booking stats
    try {
        $basicStats = $database->fetch(
            "SELECT COUNT(*) as total_bookings,
                    COALESCE(SUM(CASE WHEN status = 'COMPLETED' THEN total_amount ELSE 0 END), 0) as total_spent
             FROM bookings WHERE user_id = ?",
            [$customerId]
        );
        if ($basicStats) {
            $customerData['stats']['total_bookings'] = $basicStats['total_bookings'];
            $customerData['stats']['total_spent'] = $basicStats['total_spent'];
        }
    } catch (Exception $e) {
        // Ignore booking stats errors for now
    }
}

$customer = $customerData['customer'];
$stats = $customerData['stats'];
$recentBookings = $customerData['recent_bookings'];
$favoriteServices = $customerData['favorite_services'];

// Ensure customer has points field
if (!isset($customer['points'])) {
    $customer['points'] = 0;
}

$pageTitle = "Customer Details - " . $customer['name'];
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Medical Customer Management CSS -->
<style>
/* Medical Customer Management Specific Styles */
.medical-customer-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-customer-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.medical-customer-card:hover::before {
    left: 100%;
}

.medical-customer-card:hover {
    transform: translateY(-2px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 15px 35px var(--shadow-primary);
}

.medical-customer-item {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.medical-customer-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.medical-customer-item:hover::before {
    transform: scaleX(1);
}

.medical-customer-item:hover {
    transform: translateY(-3px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 12px 30px rgba(73, 167, 92, 0.15);
}

.medical-filter-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
}

.medical-customer-type-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.customer-type-vip {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.customer-type-regular {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

.customer-type-premium {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

.medical-status-active {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.medical-status-inactive {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
}

.medical-customer-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #49a75c, #2d6a3e);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.5rem;
    box-shadow: 0 8px 24px rgba(73, 167, 92, 0.3);
    border: 4px solid rgba(255, 255, 255, 0.8);
}

.medical-btn-primary {
    background: linear-gradient(135deg, #49a75c, #2d6a3e);
    color: white;
    border: none;
    padding: 0.875rem 1.75rem;
    border-radius: 16px;
    font-weight: 700;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(73, 167, 92, 0.3);
}

.medical-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(73, 167, 92, 0.4);
}

.medical-btn-secondary {
    background: rgba(255, 255, 255, 0.95);
    color: #49a75c;
    border: 2px solid #49a75c;
    padding: 0.875rem 1.75rem;
    border-radius: 16px;
    font-weight: 700;
    transition: all 0.4s ease;
    box-shadow: 0 4px 16px rgba(73, 167, 92, 0.1);
}

.medical-btn-secondary:hover {
    background: #49a75c;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(73, 167, 92, 0.3);
}

.medical-btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    padding: 0.625rem 1.25rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.medical-btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
}

.medical-form-input {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.medical-form-input:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
}

@media (max-width: 768px) {
    .medical-customer-grid {
        grid-template-columns: 1fr;
    }

    .medical-customer-card {
        border-radius: 16px;
        padding: 1rem;
    }

    .medical-customer-avatar {
        width: 64px;
        height: 64px;
        font-size: 1.25rem;
    }
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Medical Header -->
                    <div class="medical-customer-card p-8 mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="medical-customer-avatar">
                                        <span class="text-2xl font-bold">
                                            <?= strtoupper(substr($customer['name'] ?? 'UN', 0, 2)) ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-6">
                                    <h1 class="text-3xl font-bold text-redolence-navy">
                                        Patient Profile
                                        <span class="text-redolence-green"><?= htmlspecialchars($customer['name'] ?? 'Unknown Customer') ?></span>
                                    </h1>
                                    <p class="mt-2 text-gray-600"><?= htmlspecialchars($customer['email'] ?? 'No email') ?></p>
                                    <?php if (!empty($customer['phone'])): ?>
                                        <p class="text-gray-600"><?= htmlspecialchars($customer['phone']) ?></p>
                                    <?php endif; ?>
                                    <div class="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                                        <div class="flex items-center">
                                            <div class="w-2 h-2 bg-redolence-green rounded-full mr-2"></div>
                                            ID: <?= substr($customer['id'], 0, 8) ?>...
                                        </div>
                                        <div class="flex items-center">
                                            <div class="w-2 h-2 bg-redolence-blue rounded-full mr-2"></div>
                                            Member since <?= date('M Y', strtotime($customer['created_at'])) ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0 flex flex-col sm:flex-row gap-3">
                                <a href="<?= getBasePath() ?>/admin/customers" 
                                   class="medical-btn-secondary text-center">
                                    ← Back to Customers
                                </a>
                                <button onclick="sendMessage('<?= $customer['id'] ?>')" 
                                        class="medical-btn-primary">
                                    Send Message
                                </button>
                                <button onclick="editPoints('<?= $customer['id'] ?>', '<?= htmlspecialchars($customer['name']) ?>', <?= $customer['points'] ?>)" 
                                        class="medical-btn-primary">
                                    Edit Points
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <!-- Customer Information -->
                        <div class="lg:col-span-2">
                            <!-- Stats Cards -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                                <div class="medical-customer-item">
                                    <div class="flex items-center">
                                        <div class="p-4 rounded-full bg-gradient-to-br from-blue-100 to-blue-200">
                                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-4">
                                            <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Total Bookings</p>
                                            <p class="text-2xl font-bold text-redolence-navy"><?= number_format($stats['total_bookings']) ?></p>
                                        </div>
                                    </div>
                                </div>

                                <div class="medical-customer-item">
                                    <div class="flex items-center">
                                        <div class="p-4 rounded-full bg-gradient-to-br from-green-100 to-green-200">
                                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-4">
                                            <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Total Spent</p>
                                            <p class="text-2xl font-bold text-redolence-navy"><?= formatCurrency($stats['total_spent']) ?></p>
                                        </div>
                                    </div>
                                </div>

                                <div class="medical-customer-item">
                                    <div class="flex items-center">
                                        <div class="p-4 rounded-full bg-gradient-to-br from-purple-100 to-purple-200">
                                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-4">
                                            <p class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Loyalty Points</p>
                                            <p class="text-2xl font-bold text-redolence-navy"><?= number_format($customer['points']) ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Recent Bookings -->
                            <div class="medical-customer-card p-8 mb-8">
                                <h3 class="text-xl font-bold text-redolence-navy mb-6">Recent Appointments</h3>
                                <?php if (!empty($recentBookings)): ?>
                                    <div class="space-y-4">
                                        <?php foreach ($recentBookings as $booking): ?>
                                            <div class="medical-customer-item">
                                                <div class="flex justify-between items-start">
                                                    <div>
                                                        <?php
                                                        // Display service name or package name depending on booking type
                                                        $displayName = 'Unknown Service';
                                                        $bookingType = '';

                                                        if (!empty($booking['service_name'])) {
                                                            $displayName = $booking['service_name'];
                                                            $bookingType = 'Service';
                                                        } elseif (!empty($booking['package_name'])) {
                                                            $displayName = $booking['package_name'];
                                                            $bookingType = 'Package';
                                                        }
                                                        ?>
                                                        <div class="flex items-center gap-2">
                                                            <h4 class="text-lg font-bold text-redolence-navy"><?= htmlspecialchars($displayName) ?></h4>
                                                            <?php if ($bookingType): ?>
                                                                <span class="medical-customer-type-badge customer-type-<?= strtolower($bookingType) ?>">
                                                                    <?= $bookingType ?>
                                                                </span>
                                                            <?php endif; ?>
                                                        </div>
                                                        <p class="text-gray-600 mt-1">
                                                            <?= date('M j, Y', strtotime($booking['date'])) ?> at <?= date('g:i A', strtotime($booking['start_time'])) ?>
                                                        </p>
                                                        <?php if (!empty($booking['staff_name'])): ?>
                                                            <p class="text-gray-500 text-sm">with <?= htmlspecialchars($booking['staff_name']) ?></p>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="text-right">
                                                        <span class="medical-customer-type-badge 
                                                            <?php
                                                            switch ($booking['status']) {
                                                                case 'PENDING':
                                                                    echo 'customer-type-regular';
                                                                    break;
                                                                case 'CONFIRMED':
                                                                    echo 'customer-type-premium';
                                                                    break;
                                                                case 'COMPLETED':
                                                                    echo 'medical-status-active';
                                                                    break;
                                                                case 'CANCELLED':
                                                                    echo 'customer-type-vip';
                                                                    break;
                                                                case 'NO_SHOW':
                                                                    echo 'medical-status-inactive';
                                                                    break;
                                                                default:
                                                                    echo 'medical-status-inactive';
                                                            }
                                                            ?>">
                                                            <?= ucfirst(strtolower(str_replace('_', ' ', $booking['status']))) ?>
                                                        </span>
                                                        <p class="text-lg font-bold text-redolence-green mt-2"><?= formatCurrency($booking['total_amount']) ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <div class="mt-6">
                                        <a href="<?= getBasePath() ?>/admin/bookings?customer_id=<?= $customer['id'] ?>" 
                                           class="medical-btn-secondary">
                                            View all appointments →
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-12">
                                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        <p class="text-gray-600">No appointments found.</p>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Favorite Services & Packages -->
                            <?php if (!empty($favoriteServices)): ?>
                                <div class="medical-customer-card p-8">
                                    <h3 class="text-xl font-bold text-redolence-navy mb-6">Most Booked Treatments</h3>
                                    <div class="space-y-4">
                                        <?php foreach ($favoriteServices as $service): ?>
                                            <div class="medical-customer-item">
                                                <div class="flex justify-between items-center">
                                                    <div class="flex items-center gap-3">
                                                        <span class="text-lg font-bold text-redolence-navy"><?= htmlspecialchars($service['name'] ?? 'Unknown') ?></span>
                                                        <?php if (isset($service['type'])): ?>
                                                            <span class="medical-customer-type-badge <?= $service['type'] === 'Package' ? 'customer-type-premium' : 'customer-type-regular' ?>">
                                                                <?= $service['type'] ?>
                                                            </span>
                                                        <?php endif; ?>
                                                    </div>
                                                    <span class="text-lg font-bold text-redolence-green"><?= intval($service['booking_count'] ?? 0) ?> times</span>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Customer Details Sidebar -->
                        <div>
                            <div class="medical-customer-card p-8 mb-8">
                                <h3 class="text-xl font-bold text-redolence-navy mb-6">Patient Details</h3>
                                
                                <div class="space-y-4">
                                    <div class="medical-customer-item">
                                        <h4 class="text-sm font-semibold text-redolence-green mb-2 uppercase tracking-wide">Email</h4>
                                        <p class="text-redolence-navy font-semibold"><?= htmlspecialchars($customer['email'] ?? 'No email provided') ?></p>
                                    </div>

                                    <?php if (!empty($customer['phone'])): ?>
                                    <div class="medical-customer-item">
                                        <h4 class="text-sm font-semibold text-redolence-green mb-2 uppercase tracking-wide">Phone</h4>
                                        <p class="text-redolence-navy font-semibold"><?= htmlspecialchars($customer['phone']) ?></p>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($customer['date_of_birth'])): ?>
                                    <div class="medical-customer-item">
                                        <h4 class="text-sm font-semibold text-redolence-green mb-2 uppercase tracking-wide">Date of Birth</h4>
                                        <p class="text-redolence-navy font-semibold"><?= date('M j, Y', strtotime($customer['date_of_birth'])) ?></p>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <div class="medical-customer-item">
                                        <h4 class="text-sm font-semibold text-redolence-green mb-2 uppercase tracking-wide">Member Since</h4>
                                        <p class="text-redolence-navy font-semibold"><?= date('M j, Y', strtotime($customer['created_at'])) ?></p>
                                    </div>
                                    
                                    <?php if (!empty($stats['first_booking_date'])): ?>
                                    <div class="medical-customer-item">
                                        <h4 class="text-sm font-semibold text-redolence-green mb-2 uppercase tracking-wide">First Visit</h4>
                                        <p class="text-redolence-navy font-semibold"><?= date('M j, Y', strtotime($stats['first_booking_date'])) ?></p>
                                    </div>
                                    <?php endif; ?>

                                    <?php if (!empty($stats['last_booking_date'])): ?>
                                    <div class="medical-customer-item">
                                        <h4 class="text-sm font-semibold text-redolence-green mb-2 uppercase tracking-wide">Last Visit</h4>
                                        <p class="text-redolence-navy font-semibold"><?= date('M j, Y', strtotime($stats['last_booking_date'])) ?></p>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Quick Stats -->
                            <div class="medical-customer-card p-8">
                                <h3 class="text-xl font-bold text-redolence-navy mb-6">Quick Statistics</h3>
                                
                                <div class="space-y-4">
                                    <div class="medical-customer-item">
                                        <div class="flex justify-between">
                                            <span class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Completed Bookings</span>
                                            <span class="text-lg font-bold text-redolence-navy"><?= number_format($stats['completed_bookings']) ?></span>
                                        </div>
                                    </div>
                                    <div class="medical-customer-item">
                                        <div class="flex justify-between">
                                            <span class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Cancelled Bookings</span>
                                            <span class="text-lg font-bold text-redolence-navy"><?= number_format($stats['cancelled_bookings']) ?></span>
                                        </div>
                                    </div>
                                    <div class="medical-customer-item">
                                        <div class="flex justify-between">
                                            <span class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Average Booking Value</span>
                                            <span class="text-lg font-bold text-redolence-navy"><?= formatCurrency($stats['avg_booking_value'] ?? 0) ?></span>
                                        </div>
                                    </div>
                                    <div class="medical-customer-item">
                                        <div class="flex justify-between">
                                            <span class="text-sm font-semibold text-redolence-green uppercase tracking-wide">Loyalty Points</span>
                                            <span class="text-lg font-bold text-purple-600"><?= number_format($customer['points']) ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script>
function sendMessage(customerId) {
    window.location.href = `<?= getBasePath() ?>/admin/customers/message.php?id=${customerId}`;
}

function editPoints(customerId, customerName, currentPoints) {
    // This would open a modal or redirect to points editing page
    alert('Points editing functionality would be implemented here');
}
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>