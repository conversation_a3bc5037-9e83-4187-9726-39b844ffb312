<?php
$currentPath = $_SERVER['REQUEST_URI'];
$basePath = getBasePath();

// Normalize current path for comparison
$normalizedCurrentPath = rtrim(parse_url($currentPath, PHP_URL_PATH), '/');
$adminBasePath = $basePath . '/admin';
$navigation = [
    [
        'name' => 'Medical Dashboard',
        'href' => $basePath . '/admin',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2V7zm0 0V5a2 2 0 012-2h6l2 2h6a2 2 0 012 2v2M7 13h10M7 17h4" />',
        'description' => 'Overview & Analytics'
    ],
    [
        'name' => 'Medical Treatments',
        'href' => $basePath . '/admin/services',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />',
        'description' => 'Aesthetic Procedures'
    ],
    [
        'name' => 'Special Offers',
        'href' => $basePath . '/admin/offers',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />',
        'description' => 'Promotions & Discounts'
    ],
    [
        'name' => 'Treatment Packages',
        'href' => $basePath . '/admin/packages',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />',
        'description' => 'Medical Packages'
    ],
    [
        'name' => 'Patient Appointments',
        'href' => $basePath . '/admin/bookings',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />',
        'description' => 'Scheduling & Calendar'
    ],
    [
        'name' => 'Rooms Management',
        'href' => $basePath . '/admin/rooms',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />',
        'description' => 'Treatment Rooms & Facilities'
    ],
    [
        'name' => 'Patient Management',
        'href' => $basePath . '/admin/customers',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />',
        'description' => 'Patient Records'
    ],
    [
        'name' => 'Patient Reviews',
        'href' => $basePath . '/admin/reviews',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />',
        'description' => 'Testimonials & Feedback'
    ],
    [
        'name' => 'Revenue & Analytics',
        'href' => $basePath . '/admin/earnings',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />',
        'description' => 'Financial Reports'
    ],
    [
        'name' => 'Loyalty Program',
        'href' => $basePath . '/admin/rewards',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />',
        'description' => 'Patient Rewards'
    ],
    [
        'name' => 'Medical Staff',
        'href' => $basePath . '/admin/staff',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />',
        'description' => 'Medical Professionals'
    ],
    [
        'name' => 'Medical Notifications',
        'href' => $basePath . '/admin/notifications',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v2.25H2.25v-2.25L4.5 12V9.75a6 6 0 0 1 6-6z" />',
        'description' => 'System Alerts'
    ],
    [
        'name' => 'Patient Inquiries',
        'href' => $basePath . '/admin/contact-messages',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />',
        'description' => 'Contact Messages'
    ],
    [
        'name' => 'Medical FAQ',
        'href' => $basePath . '/admin/faq',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />',
        'description' => 'Patient Questions'
    ],
    [
        'name' => 'Medical Gallery',
        'href' => $basePath . '/admin/cms',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />',
        'description' => 'Content Management'
    ],
    [
        'name' => 'Medical Blog',
        'href' => $basePath . '/admin/blog',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />',
        'description' => 'Educational Content'
    ],
    [
        'name' => 'System Settings',
        'href' => $basePath . '/admin/settings',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />',
        'description' => 'Configuration'
    ]
];
?>

<!-- Clean Professional Sidebar -->
<div class="clean-sidebar h-full flex flex-col">
    <!-- Clean Logo Section -->
    <div class="p-4 border-b border-gray-100">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-redolence-green rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
            </div>
            <div>
                <h2 class="text-lg font-semibold text-gray-900">Redolence</h2>
                <p class="text-xs text-gray-500">Medical Admin</p>
            </div>
        </div>
    </div>

    <!-- Clean Navigation -->
    <nav class="flex-1 p-4 space-y-1 overflow-y-auto">
    <?php foreach ($navigation as $item):
        // Normalize item href for comparison
        $normalizedItemHref = rtrim($item['href'], '/');

        // Check if this menu item is active
        $isActive = false;

        // Exact match for all pages
        if ($normalizedCurrentPath === $normalizedItemHref) {
            $isActive = true;
        }
        // For non-dashboard items, check sub-pages
        elseif ($normalizedItemHref !== $adminBasePath && strpos($normalizedCurrentPath . '/', $normalizedItemHref . '/') === 0) {
            $isActive = true;
        }
    ?>
        <a href="<?= $item['href'] ?>" class="clean-nav-item <?= $isActive ? 'active' : '' ?>">
            <svg class="clean-icon mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <?= $item['icon'] ?>
            </svg>
            <div class="flex-1">
                <div class="font-medium"><?= htmlspecialchars($item['name']) ?></div>
                <div class="text-xs opacity-75"><?= htmlspecialchars($item['description']) ?></div>
            </div>
        </a>
    <?php endforeach; ?>
</nav>

    <!-- Clean Analytics -->
    <div class="mt-6 px-4">
        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Analytics</h3>
        <div class="space-y-3">
            <?php
            // Get quick stats
            $todayAppointments = $database->fetch("SELECT COUNT(*) as count FROM bookings WHERE DATE(date) = CURDATE()")['count'];
            $pendingAppointments = $database->fetch("SELECT COUNT(*) as count FROM bookings WHERE status = 'PENDING'")['count'];
            $todayRevenue = $database->fetch("SELECT SUM(total_amount) as total FROM bookings WHERE DATE(date) = CURDATE() AND status = 'COMPLETED'")['total'] ?? 0;
            ?>

            <div class="clean-stat-card">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">Today's Appointments</span>
                    </div>
                    <span class="text-lg font-semibold text-gray-900"><?= $todayAppointments ?></span>
                </div>
            </div>

            <div class="clean-stat-card">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">Pending</span>
                    </div>
                    <span class="text-lg font-semibold text-gray-900"><?= $pendingAppointments ?></span>
                </div>
            </div>

            <div class="clean-stat-card">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">Revenue</span>
                    </div>
                    <span class="text-sm font-semibold text-gray-900"><?= formatCurrency($todayRevenue) ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Clean System Status -->
    <div class="mt-6 px-4">
        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">System Status</h3>
        <div class="space-y-2">
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span class="text-gray-600">Medical Systems</span>
                </div>
                <span class="text-green-600 font-medium">Online</span>
            </div>

            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span class="text-gray-600">Database</span>
                </div>
                <span class="text-blue-600 font-medium">Active</span>
            </div>

            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                    <span class="text-gray-600">Payments</span>
                </div>
                <span class="text-emerald-600 font-medium">Live</span>
            </div>

            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span class="text-gray-600">Appointments</span>
                </div>
                <span class="text-purple-600 font-medium">Ready</span>
            </div>
        </div>
    </div>

    <!-- Clean Quick Actions -->
    <div class="mt-6 px-4 pb-4">
        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Quick Actions</h3>
        <div class="space-y-2">
            <a href="<?= $basePath ?>/admin/bookings/create" class="clean-btn w-full">
                <svg class="clean-icon mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                New Appointment
            </a>

            <a href="<?= $basePath ?>/admin/customers/create" class="clean-btn-ghost w-full">
                <svg class="clean-icon mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                Add Patient
            </a>
        </div>
    </div>

</div> <!-- End of Clean Professional Sidebar -->