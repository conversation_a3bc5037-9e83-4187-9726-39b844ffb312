<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME . ' Admin' : APP_NAME . ' - Medical Admin Panel' ?></title>
    <meta name="description" content="<?= isset($pageDescription) ? $pageDescription : 'Medical admin panel for Redolence Medi Aesthetics - Advanced medical beauty management system' ?>">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?= getBasePath() ?>/includes/redolence_logo.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'redolence-green': '#49a75c',
                        'redolence-blue': '#5894d2',
                        'redolence-gold': '#f4d03f',
                        'redolence-navy': '#1a2332',
                        'redolence-gray': '#f8fafc',
                        primary: {
                            50: '#f0f9f3',
                            100: '#dcf2e3',
                            200: '#bce5cb',
                            300: '#8dd2a8',
                            400: '#5ab67e',
                            500: '#49a75c',
                            600: '#3a8549',
                            700: '#316a3c',
                            800: '#2b5533',
                            900: '#25462c',
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                        },
                        medical: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#5894d2',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                    },
                    fontFamily: {
                        sans: ['Inter', 'ui-sans-serif', 'system-ui'],
                        serif: ['Playfair Display', 'ui-serif', 'Georgia'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'scale-in': 'scaleIn 0.3s ease-out',
                        'pulse-medical': 'pulseMedical 2s ease-in-out infinite',
                        'float': 'float 6s ease-in-out infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        scaleIn: {
                            '0%': { transform: 'scale(0.95)', opacity: '0' },
                            '100%': { transform: 'scale(1)', opacity: '1' },
                        },
                        pulseMedical: {
                            '0%, 100%': { boxShadow: '0 0 20px rgba(73, 167, 92, 0.3)' },
                            '50%': { boxShadow: '0 0 30px rgba(88, 148, 210, 0.5)' },
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' },
                        },
                    },
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <style>
        :root {
            --background: #ffffff;
            --foreground: #1a2332;
            --primary-green: #49a75c;
            --primary-blue: #5894d2;
            --accent-gold: #f4d03f;
            --deep-navy: #1a2332;
            --soft-gray: #f8fafc;
            --medical-white: #ffffff;
            --shadow-primary: rgba(73, 167, 92, 0.15);
            --shadow-blue: rgba(88, 148, 210, 0.15);
            --gradient-primary: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
            --gradient-soft: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
        }

        * {
            box-sizing: border-box;
            padding: 0;
            margin: 0;
        }

        html, body {
            max-width: 100vw;
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        body {
            background: var(--background);
            color: var(--foreground);
            font-family: 'Inter', ui-sans-serif, system-ui;
            line-height: 1.6;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--soft-gray);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-green);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-blue);
        }

        /* Selection */
        ::selection {
            background: var(--primary-green);
            color: var(--medical-white);
        }

        /* Medical glass effect for headers and cards */
        .medical-glass {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(73, 167, 92, 0.1);
        }

        /* Medical hover effects */
        .medical-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .medical-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px var(--shadow-primary);
        }

        /* Medical admin specific styles */
        .admin-page {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f0f9f3 100%);
            min-height: 100vh;
        }

        .admin-header {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
            backdrop-filter: blur(20px);
            border-bottom: 2px solid rgba(73, 167, 92, 0.1);
        }

        .admin-sidebar {
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
            backdrop-filter: blur(15px);
            border-right: 2px solid rgba(73, 167, 92, 0.1);
        }

        /* Medical navigation styles */
        .nav-item-active {
            background: var(--gradient-primary);
            color: white;
            box-shadow: 0 4px 15px var(--shadow-primary);
        }

        .nav-item-inactive {
            color: var(--deep-navy);
            border: 1px solid transparent;
        }

        .nav-item-inactive:hover {
            background: var(--gradient-soft);
            color: var(--primary-green);
            border-color: rgba(73, 167, 92, 0.2);
        }

        /* Medical notification styles */
        .notification-badge {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            animation: pulse-medical 2s ease-in-out infinite;
        }

        /* Comprehensive Medical Button System */
        .medical-btn-primary {
            background: linear-gradient(135deg, #49a75c, #2d6a3e);
            color: white;
            border: none;
            padding: 0.875rem 1.75rem;
            border-radius: 16px;
            font-weight: 700;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 6px 20px rgba(73, 167, 92, 0.3);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .medical-btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(73, 167, 92, 0.4);
            color: white;
            text-decoration: none;
        }

        .medical-btn-secondary {
            background: rgba(255, 255, 255, 0.95);
            color: #49a75c;
            border: 2px solid #49a75c;
            padding: 0.875rem 1.75rem;
            border-radius: 16px;
            font-weight: 700;
            transition: all 0.4s ease;
            box-shadow: 0 4px 16px rgba(73, 167, 92, 0.1);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .medical-btn-secondary:hover {
            background: #49a75c;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(73, 167, 92, 0.3);
            text-decoration: none;
        }

        .medical-btn-danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            border: none;
            padding: 0.875rem 1.75rem;
            border-radius: 16px;
            font-weight: 700;
            transition: all 0.4s ease;
            box-shadow: 0 4px 16px rgba(239, 68, 68, 0.2);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .medical-btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
            color: white;
            text-decoration: none;
        }

        /* Medical Card System */
        .medical-card {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
            backdrop-filter: blur(20px);
            border: 2px solid rgba(73, 167, 92, 0.1);
            border-radius: 20px;
            transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
            position: relative;
            overflow: hidden;
        }

        .medical-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 3px;
            background: var(--gradient-primary);
            transition: left 0.6s ease;
        }

        .medical-card:hover::before {
            left: 100%;
        }

        .medical-card:hover {
            transform: translateY(-2px);
            border-color: rgba(73, 167, 92, 0.2);
            box-shadow: 0 15px 35px var(--shadow-primary);
        }

        .medical-filter-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
            backdrop-filter: blur(20px);
            border: 2px solid rgba(73, 167, 92, 0.1);
            border-radius: 20px;
        }

        .medical-content-card {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
            border: 1px solid rgba(73, 167, 92, 0.1);
            border-radius: 16px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .medical-content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .medical-content-card:hover::before {
            transform: scaleX(1);
        }

        .medical-content-card:hover {
            transform: translateY(-3px);
            border-color: rgba(73, 167, 92, 0.2);
            box-shadow: 0 12px 30px rgba(73, 167, 92, 0.15);
        }

        /* Medical Form System */
        .medical-form-input {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(73, 167, 92, 0.1);
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            color: var(--deep-navy);
            font-weight: 500;
        }

        .medical-form-input:focus {
            outline: none;
            border-color: var(--primary-green);
            box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
            background: white;
        }

        .medical-form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--primary-green);
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Medical Message System */
        .medical-message-success {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.05));
            border: 2px solid rgba(16, 185, 129, 0.2);
            border-radius: 16px;
            padding: 1rem 1.5rem;
            color: #065f46;
            font-weight: 600;
        }

        .medical-message-error {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.05));
            border: 2px solid rgba(239, 68, 68, 0.2);
            border-radius: 16px;
            padding: 1rem 1.5rem;
            color: #7f1d1d;
            font-weight: 600;
        }

        /* Clean Modern Design System */
        .clean-header {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.06);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .clean-sidebar {
            background: rgba(255, 255, 255, 0.98);
            border-right: 1px solid rgba(0, 0, 0, 0.06);
        }

        .clean-card {
            background: white;
            border: 1px solid rgba(0, 0, 0, 0.06);
            border-radius: 8px;
            transition: all 0.15s ease;
        }

        .clean-card:hover {
            border-color: rgba(73, 167, 92, 0.15);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .clean-btn {
            background: #49a75c;
            color: white;
            border: none;
            padding: 0.625rem 1.25rem;
            border-radius: 6px;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.15s ease;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .clean-btn:hover {
            background: #3d8a4d;
            color: white;
            text-decoration: none;
        }

        .clean-btn-ghost {
            background: transparent;
            color: #6b7280;
            border: 1px solid rgba(0, 0, 0, 0.08);
            padding: 0.625rem 1.25rem;
            border-radius: 6px;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.15s ease;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .clean-btn-ghost:hover {
            background: #f9fafb;
            color: #374151;
            text-decoration: none;
        }

        .clean-nav-item {
            color: #6b7280;
            border-radius: 6px;
            padding: 0.75rem 1rem;
            transition: all 0.15s ease;
            font-weight: 500;
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .clean-nav-item:hover {
            background: rgba(73, 167, 92, 0.06);
            color: #49a75c;
            text-decoration: none;
        }

        .clean-nav-item.active {
            background: #49a75c;
            color: white;
        }

        .clean-stat-card {
            background: white;
            border: 1px solid rgba(0, 0, 0, 0.06);
            border-radius: 8px;
            padding: 1.25rem;
            transition: all 0.15s ease;
        }

        .clean-stat-card:hover {
            border-color: rgba(73, 167, 92, 0.12);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .clean-icon {
            width: 1.25rem;
            height: 1.25rem;
            color: currentColor;
        }

        .clean-avatar {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background: #49a75c;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.75rem;
        }
    </style>

    <!-- AJAX Reminder Processor -->
    <script src="<?= getBasePath() ?>/assets/js/reminder-processor.js"></script>
</head>
<body class="antialiased min-h-screen admin-page">
    <!-- Clean Professional Header -->
    <header class="clean-header fixed top-0 left-0 right-0 z-50">
        <div class="max-w-7xl mx-auto px-4 lg:px-6">
            <div class="flex justify-between items-center h-16">
                <!-- Clean Logo Section -->
                <div class="flex items-center space-x-4">
                    <!-- Mobile Menu -->
                    <button id="mobile-menu-toggle" class="lg:hidden clean-btn-ghost p-2">
                        <svg class="clean-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>

                    <!-- Logo -->
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-redolence-green rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                            </svg>
                        </div>
                        <div class="hidden sm:block">
                            <h1 class="text-lg font-semibold text-gray-900">Redolence</h1>
                            <p class="text-xs text-gray-500">Medical Admin</p>
                        </div>
                    </div>
                </div>

                <!-- Clean User Menu -->
                <div class="flex items-center space-x-3">
                    <!-- Notifications -->
                    <div class="relative">
                        <button id="notificationButton" onclick="toggleNotifications()"
                                class="clean-btn-ghost p-2 relative">
                            <svg class="clean-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v2.25H2.25v-2.25L4.5 12V9.75a6 6 0 0 1 6-6z" />
                            </svg>
                            <span id="notificationCounter" class="notification-badge absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 text-white text-xs font-medium flex items-center justify-center hidden">0</span>
                        </button>

                        <!-- Clean Notification Dropdown -->
                        <div id="notificationDropdown" class="hidden absolute right-0 mt-2 w-80 clean-card shadow-lg z-50 max-h-96 overflow-hidden">
                            <!-- Header -->
                            <div class="px-4 py-3 border-b border-gray-100">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-sm font-semibold text-gray-900">Notifications</h3>
                                    <div class="flex items-center space-x-2">
                                        <button onclick="markAllAsRead()" class="text-xs text-gray-500 hover:text-gray-700">
                                            Mark all read
                                        </button>
                                        <button onclick="openNotificationsPage()" class="text-xs text-redolence-green hover:text-redolence-green/80">
                                            View all
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Category Tabs -->
                            <div class="px-4 py-2 border-b border-redolence-green/10">
                                <div class="flex space-x-1 text-xs">
                                    <button onclick="filterNotifications('all')" class="notification-tab active px-3 py-1 rounded-md bg-redolence-green text-white font-medium">
                                        All <span id="count-all">0</span>
                                    </button>
                                    <button onclick="filterNotifications('BOOKING')" class="notification-tab px-3 py-1 rounded-md text-gray-600 hover:text-redolence-green transition-colors">
                                        Appointments <span id="count-BOOKING">0</span>
                                    </button>
                                    <button onclick="filterNotifications('CUSTOMER')" class="notification-tab px-3 py-1 rounded-md text-gray-600 hover:text-redolence-green transition-colors">
                                        Patients <span id="count-CUSTOMER">0</span>
                                    </button>
                                    <button onclick="filterNotifications('SYSTEM')" class="notification-tab px-3 py-1 rounded-md text-gray-600 hover:text-redolence-green transition-colors">
                                        System <span id="count-SYSTEM">0</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Notifications List -->
                            <div id="notificationsList" class="max-h-64 overflow-y-auto">
                                <div class="p-4 text-center text-gray-500">
                                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-redolence-green mx-auto mb-2"></div>
                                    Loading medical notifications...
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Clean User Profile -->
                    <div class="relative">
                        <button id="user-menu-toggle" class="clean-btn-ghost flex items-center space-x-2 p-2">
                            <div class="clean-avatar">
                                <?= strtoupper(substr(getCurrentUser()['name'], 0, 2)) ?>
                            </div>
                            <div class="hidden md:block text-left">
                                <p class="text-sm font-medium text-gray-900"><?= htmlspecialchars(getCurrentUser()['name']) ?></p>
                                <p class="text-xs text-gray-500">Admin</p>
                            </div>
                            <svg class="clean-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>

                        <!-- Clean User Menu -->
                        <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 clean-card shadow-lg z-50">
                            <?php $basePath = getBasePath(); ?>

                            <!-- User Info -->
                            <div class="px-3 py-2 border-b border-gray-100">
                                <div class="flex items-center space-x-2">
                                    <div class="clean-avatar text-xs">
                                        <?= strtoupper(substr(getCurrentUser()['name'], 0, 2)) ?>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900"><?= htmlspecialchars(getCurrentUser()['name']) ?></p>
                                        <p class="text-xs text-gray-500">Administrator</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Menu Items -->
                            <div class="py-1">
                                <a href="<?= $basePath ?>/admin/profile" class="clean-nav-item">
                                    <svg class="clean-icon mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    Profile
                                </a>

                                <a href="<?= $basePath ?>/admin/settings" class="clean-nav-item">
                                    <svg class="clean-icon mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    Settings
                                </a>

                                <div class="border-t border-gray-100 my-1"></div>

                                <a href="<?= $basePath ?>/" class="clean-nav-item">
                                    <svg class="clean-icon mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                    </svg>
                                    View Website
                                </a>

                                <a href="<?= $basePath ?>/auth/logout.php" class="clean-nav-item text-red-600 hover:bg-red-50 hover:text-red-700">
                                    <svg class="clean-icon mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                    </svg>
                                    Logout
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Sidebar Overlay -->
    <div id="mobile-sidebar-overlay" class="hidden fixed inset-0 z-40 lg:hidden">
        <div class="fixed inset-0 bg-black opacity-50"></div>
    </div>

    <!-- Mobile Medical Sidebar -->
    <div id="mobile-sidebar" class="hidden fixed inset-y-0 left-0 z-50 w-64 admin-sidebar lg:hidden">
        <div class="flex items-center justify-between p-4 border-b border-redolence-green/10">
            <h2 class="text-lg font-semibold text-redolence-green">Medical Navigation</h2>
            <button id="mobile-sidebar-close" class="p-2 rounded-md text-gray-600 hover:text-redolence-green hover:bg-redolence-green/10">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <nav class="mt-4">
            <?php include __DIR__ . '/admin_sidebar_nav.php'; ?>
        </nav>
    </div>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-toggle').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.remove('hidden');
            document.getElementById('mobile-sidebar-overlay').classList.remove('hidden');
        });

        document.getElementById('mobile-sidebar-close').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.add('hidden');
            document.getElementById('mobile-sidebar-overlay').classList.add('hidden');
        });

        document.getElementById('mobile-sidebar-overlay').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.add('hidden');
            document.getElementById('mobile-sidebar-overlay').classList.add('hidden');
        });

        // User menu toggle
        document.getElementById('user-menu-toggle').addEventListener('click', function() {
            const menu = document.getElementById('user-menu');
            menu.classList.toggle('hidden');
        });

        // Close user menu when clicking outside
        document.addEventListener('click', function(event) {
            const userMenuToggle = document.getElementById('user-menu-toggle');
            const userMenu = document.getElementById('user-menu');
            
            if (!userMenuToggle.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });
    </script>