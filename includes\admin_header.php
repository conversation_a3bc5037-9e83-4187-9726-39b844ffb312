<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME . ' Admin' : APP_NAME . ' - Medical Admin Panel' ?></title>
    <meta name="description" content="<?= isset($pageDescription) ? $pageDescription : 'Medical admin panel for Redolence Medi Aesthetics - Advanced medical beauty management system' ?>">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?= getBasePath() ?>/includes/redolence_logo.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'redolence-green': '#49a75c',
                        'redolence-blue': '#5894d2',
                        'redolence-gold': '#f4d03f',
                        'redolence-navy': '#1a2332',
                        'redolence-gray': '#f8fafc',
                        primary: {
                            50: '#f0f9f3',
                            100: '#dcf2e3',
                            200: '#bce5cb',
                            300: '#8dd2a8',
                            400: '#5ab67e',
                            500: '#49a75c',
                            600: '#3a8549',
                            700: '#316a3c',
                            800: '#2b5533',
                            900: '#25462c',
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                        },
                        medical: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#5894d2',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                    },
                    fontFamily: {
                        sans: ['Inter', 'ui-sans-serif', 'system-ui'],
                        serif: ['Playfair Display', 'ui-serif', 'Georgia'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'scale-in': 'scaleIn 0.3s ease-out',
                        'pulse-medical': 'pulseMedical 2s ease-in-out infinite',
                        'float': 'float 6s ease-in-out infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        scaleIn: {
                            '0%': { transform: 'scale(0.95)', opacity: '0' },
                            '100%': { transform: 'scale(1)', opacity: '1' },
                        },
                        pulseMedical: {
                            '0%, 100%': { boxShadow: '0 0 20px rgba(73, 167, 92, 0.3)' },
                            '50%': { boxShadow: '0 0 30px rgba(88, 148, 210, 0.5)' },
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' },
                        },
                    },
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <style>
        :root {
            --background: #ffffff;
            --foreground: #1a2332;
            --primary-green: #49a75c;
            --primary-blue: #5894d2;
            --accent-gold: #f4d03f;
            --deep-navy: #1a2332;
            --soft-gray: #f8fafc;
            --medical-white: #ffffff;
            --shadow-primary: rgba(73, 167, 92, 0.15);
            --shadow-blue: rgba(88, 148, 210, 0.15);
            --gradient-primary: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
            --gradient-soft: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
        }

        * {
            box-sizing: border-box;
            padding: 0;
            margin: 0;
        }

        html, body {
            max-width: 100vw;
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        body {
            background: var(--background);
            color: var(--foreground);
            font-family: 'Inter', ui-sans-serif, system-ui;
            line-height: 1.6;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--soft-gray);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-green);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-blue);
        }

        /* Selection */
        ::selection {
            background: var(--primary-green);
            color: var(--medical-white);
        }

        /* Medical glass effect for headers and cards */
        .medical-glass {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(73, 167, 92, 0.1);
        }

        /* Medical hover effects */
        .medical-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .medical-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px var(--shadow-primary);
        }

        /* Medical admin specific styles */
        .admin-page {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f0f9f3 100%);
            min-height: 100vh;
        }

        .admin-header {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
            backdrop-filter: blur(25px);
            border-bottom: 3px solid transparent;
            border-image: linear-gradient(90deg, var(--primary-green), var(--primary-blue), var(--accent-gold)) 1;
            box-shadow: 0 8px 32px rgba(73, 167, 92, 0.15), 0 4px 16px rgba(88, 148, 210, 0.1);
        }

        .admin-sidebar {
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
            backdrop-filter: blur(15px);
            border-right: 2px solid rgba(73, 167, 92, 0.1);
        }

        /* Medical navigation styles */
        .nav-item-active {
            background: var(--gradient-primary);
            color: white;
            box-shadow: 0 4px 15px var(--shadow-primary);
        }

        .nav-item-inactive {
            color: var(--deep-navy);
            border: 1px solid transparent;
        }

        .nav-item-inactive:hover {
            background: var(--gradient-soft);
            color: var(--primary-green);
            border-color: rgba(73, 167, 92, 0.2);
        }

        /* Luxury Medical notification styles */
        .notification-badge {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            animation: pulse-luxury 2s ease-in-out infinite;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.9);
        }

        @keyframes pulse-luxury {
            0%, 100% {
                box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3), 0 0 20px rgba(239, 68, 68, 0.2);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 4px 16px rgba(239, 68, 68, 0.5), 0 0 30px rgba(239, 68, 68, 0.3);
                transform: scale(1.05);
            }
        }

        /* Luxury notification tab styles */
        .notification-tab {
            transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
            position: relative;
            overflow: hidden;
        }

        .notification-tab.active {
            background: var(--gradient-primary);
            color: white;
            box-shadow: 0 4px 15px rgba(73, 167, 92, 0.3);
            transform: translateY(-1px);
        }

        .notification-tab:not(.active):hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(73, 167, 92, 0.15);
        }

        /* Comprehensive Medical Button System */
        .medical-btn-primary {
            background: linear-gradient(135deg, #49a75c, #2d6a3e);
            color: white;
            border: none;
            padding: 0.875rem 1.75rem;
            border-radius: 16px;
            font-weight: 700;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 6px 20px rgba(73, 167, 92, 0.3);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .medical-btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(73, 167, 92, 0.4);
            color: white;
            text-decoration: none;
        }

        .medical-btn-secondary {
            background: rgba(255, 255, 255, 0.95);
            color: #49a75c;
            border: 2px solid #49a75c;
            padding: 0.875rem 1.75rem;
            border-radius: 16px;
            font-weight: 700;
            transition: all 0.4s ease;
            box-shadow: 0 4px 16px rgba(73, 167, 92, 0.1);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .medical-btn-secondary:hover {
            background: #49a75c;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(73, 167, 92, 0.3);
            text-decoration: none;
        }

        .medical-btn-danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            border: none;
            padding: 0.875rem 1.75rem;
            border-radius: 16px;
            font-weight: 700;
            transition: all 0.4s ease;
            box-shadow: 0 4px 16px rgba(239, 68, 68, 0.2);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .medical-btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
            color: white;
            text-decoration: none;
        }

        /* Medical Card System */
        .medical-card {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
            backdrop-filter: blur(20px);
            border: 2px solid rgba(73, 167, 92, 0.1);
            border-radius: 20px;
            transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
            position: relative;
            overflow: hidden;
        }

        .medical-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 3px;
            background: var(--gradient-primary);
            transition: left 0.6s ease;
        }

        .medical-card:hover::before {
            left: 100%;
        }

        .medical-card:hover {
            transform: translateY(-2px);
            border-color: rgba(73, 167, 92, 0.2);
            box-shadow: 0 15px 35px var(--shadow-primary);
        }

        .medical-filter-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
            backdrop-filter: blur(20px);
            border: 2px solid rgba(73, 167, 92, 0.1);
            border-radius: 20px;
        }

        .medical-content-card {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
            border: 1px solid rgba(73, 167, 92, 0.1);
            border-radius: 16px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .medical-content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .medical-content-card:hover::before {
            transform: scaleX(1);
        }

        .medical-content-card:hover {
            transform: translateY(-3px);
            border-color: rgba(73, 167, 92, 0.2);
            box-shadow: 0 12px 30px rgba(73, 167, 92, 0.15);
        }

        /* Medical Form System */
        .medical-form-input {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(73, 167, 92, 0.1);
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            color: var(--deep-navy);
            font-weight: 500;
        }

        .medical-form-input:focus {
            outline: none;
            border-color: var(--primary-green);
            box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
            background: white;
        }

        .medical-form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--primary-green);
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Medical Message System */
        .medical-message-success {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.05));
            border: 2px solid rgba(16, 185, 129, 0.2);
            border-radius: 16px;
            padding: 1rem 1.5rem;
            color: #065f46;
            font-weight: 600;
        }

        .medical-message-error {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.05));
            border: 2px solid rgba(239, 68, 68, 0.2);
            border-radius: 16px;
            padding: 1rem 1.5rem;
            color: #7f1d1d;
            font-weight: 600;
        }

        /* Sophisticated Medical Header Design System */
        .medical-luxury-header {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.98) 0%,
                rgba(248, 250, 252, 0.96) 25%,
                rgba(240, 249, 243, 0.94) 50%,
                rgba(248, 250, 252, 0.96) 75%,
                rgba(255, 255, 255, 0.98) 100%);
            backdrop-filter: blur(25px);
            border-bottom: 3px solid transparent;
            border-image: linear-gradient(90deg, var(--primary-green), var(--primary-blue), var(--accent-gold)) 1;
            box-shadow:
                0 8px 32px rgba(73, 167, 92, 0.15),
                0 4px 16px rgba(88, 148, 210, 0.1),
                0 2px 8px rgba(244, 208, 63, 0.05);
            position: relative;
            overflow: hidden;
        }

        .medical-luxury-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(73, 167, 92, 0.03) 25%,
                rgba(88, 148, 210, 0.03) 50%,
                rgba(244, 208, 63, 0.03) 75%,
                transparent 100%);
            animation: shimmer 8s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { left: -100%; }
            50% { left: 100%; }
        }

        .medical-luxury-card {
            background: linear-gradient(145deg,
                rgba(255, 255, 255, 0.98),
                rgba(248, 250, 252, 0.95));
            backdrop-filter: blur(20px);
            border: 2px solid rgba(73, 167, 92, 0.1);
            border-radius: 16px;
            box-shadow:
                0 8px 25px rgba(73, 167, 92, 0.1),
                0 4px 12px rgba(88, 148, 210, 0.08);
            transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
            position: relative;
            overflow: hidden;
        }

        .medical-luxury-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .medical-luxury-card:hover::before {
            transform: scaleX(1);
        }

        .medical-luxury-card:hover {
            transform: translateY(-2px);
            border-color: rgba(73, 167, 92, 0.2);
            box-shadow:
                0 12px 35px rgba(73, 167, 92, 0.15),
                0 6px 18px rgba(88, 148, 210, 0.12),
                0 3px 9px rgba(244, 208, 63, 0.08);
        }

        .medical-luxury-btn {
            background: linear-gradient(135deg, var(--primary-green), #3d8a4d);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(73, 167, 92, 0.2);
            position: relative;
            overflow: hidden;
        }

        .medical-luxury-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .medical-luxury-btn:hover::before {
            left: 100%;
        }

        .medical-luxury-btn:hover {
            background: linear-gradient(135deg, #3d8a4d, var(--primary-blue));
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(73, 167, 92, 0.3);
        }

        .medical-luxury-btn-ghost {
            background: linear-gradient(145deg,
                rgba(255, 255, 255, 0.9),
                rgba(248, 250, 252, 0.8));
            color: var(--deep-navy);
            border: 2px solid rgba(73, 167, 92, 0.15);
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(73, 167, 92, 0.1);
            position: relative;
            overflow: hidden;
        }

        .medical-luxury-btn-ghost::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .medical-luxury-btn-ghost:hover::before {
            opacity: 0.1;
        }

        .medical-luxury-btn-ghost:hover {
            background: linear-gradient(145deg,
                rgba(73, 167, 92, 0.05),
                rgba(88, 148, 210, 0.05));
            color: var(--primary-green);
            text-decoration: none;
            transform: translateY(-1px);
            border-color: rgba(73, 167, 92, 0.3);
            box-shadow: 0 6px 20px rgba(73, 167, 92, 0.15);
        }

        .medical-luxury-nav-item {
            color: var(--deep-navy);
            border-radius: 12px;
            padding: 0.875rem 1.25rem;
            transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
            font-weight: 600;
            text-decoration: none;
            display: flex;
            align-items: center;
            background: linear-gradient(145deg,
                rgba(255, 255, 255, 0.6),
                rgba(248, 250, 252, 0.4));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(73, 167, 92, 0.1);
            position: relative;
            overflow: hidden;
        }

        .medical-luxury-nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(73, 167, 92, 0.1),
                transparent);
            transition: left 0.5s ease;
        }

        .medical-luxury-nav-item:hover::before {
            left: 100%;
        }

        .medical-luxury-nav-item:hover {
            background: linear-gradient(145deg,
                rgba(73, 167, 92, 0.08),
                rgba(88, 148, 210, 0.06));
            color: var(--primary-green);
            text-decoration: none;
            transform: translateY(-1px);
            border-color: rgba(73, 167, 92, 0.2);
            box-shadow: 0 6px 20px rgba(73, 167, 92, 0.15);
        }

        .medical-luxury-nav-item.active {
            background: var(--gradient-primary);
            color: white;
            border-color: transparent;
            box-shadow: 0 8px 25px rgba(73, 167, 92, 0.3);
        }

        .medical-luxury-icon {
            width: 1.25rem;
            height: 1.25rem;
            color: currentColor;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
        }

        .medical-luxury-avatar {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            background: var(--gradient-primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 0.875rem;
            box-shadow:
                0 4px 15px rgba(73, 167, 92, 0.2),
                0 2px 8px rgba(88, 148, 210, 0.15);
            border: 3px solid rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .medical-luxury-avatar::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg,
                transparent 30%,
                rgba(255, 255, 255, 0.3) 50%,
                transparent 70%);
            transform: rotate(-45deg);
            transition: transform 0.6s ease;
        }

        .medical-luxury-avatar:hover::before {
            transform: rotate(-45deg) translate(50%, 50%);
        }

        .medical-luxury-avatar:hover {
            transform: scale(1.05);
            box-shadow:
                0 6px 20px rgba(73, 167, 92, 0.3),
                0 3px 12px rgba(88, 148, 210, 0.2);
        }
    </style>

    <!-- AJAX Reminder Processor -->
    <script src="<?= getBasePath() ?>/assets/js/reminder-processor.js"></script>
</head>
<body class="antialiased min-h-screen admin-page">
    <!-- Sophisticated Medical Luxury Header -->
    <header class="medical-luxury-header fixed top-0 left-0 right-0 z-50">
        <div class="max-w-7xl mx-auto px-4 lg:px-6">
            <div class="flex justify-between items-center h-20">
                <!-- Luxury Logo Section -->
                <div class="flex items-center space-x-6">
                    <!-- Mobile Menu -->
                    <button id="mobile-menu-toggle" class="lg:hidden medical-luxury-btn-ghost p-3">
                        <svg class="medical-luxury-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>

                    <!-- Premium Logo -->
                    <div class="flex items-center space-x-4">
                        <div class="relative">
                            <div class="w-12 h-12 bg-gradient-to-br from-redolence-green via-redolence-blue to-redolence-gold rounded-2xl flex items-center justify-center shadow-lg transform transition-all duration-300 hover:scale-105 hover:rotate-3">
                                <svg class="w-7 h-7 text-white filter drop-shadow-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                </svg>
                            </div>
                            <div class="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-redolence-gold to-yellow-400 rounded-full animate-pulse"></div>
                        </div>
                        <div class="hidden sm:block">
                            <h1 class="text-2xl font-bold bg-gradient-to-r from-redolence-navy via-redolence-green to-redolence-blue bg-clip-text text-transparent">
                                Redolence
                            </h1>
                            <p class="text-sm font-semibold text-redolence-blue/80 tracking-wide">Medical Aesthetics Admin</p>
                        </div>
                    </div>
                </div>

                <!-- Luxury User Menu -->
                <div class="flex items-center space-x-4">
                    <!-- Premium Notifications -->
                    <div class="relative">
                        <button id="notificationButton" onclick="toggleNotifications()"
                                class="medical-luxury-btn-ghost p-3 relative group">
                            <svg class="medical-luxury-icon group-hover:scale-110 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v2.25H2.25v-2.25L4.5 12V9.75a6 6 0 0 1 6-6z" />
                            </svg>
                            <span id="notificationCounter" class="notification-badge absolute -top-2 -right-2 h-5 w-5 rounded-full bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-bold flex items-center justify-center hidden shadow-lg border-2 border-white">0</span>
                        </button>

                        <!-- Luxury Notification Dropdown -->
                        <div id="notificationDropdown" class="hidden absolute right-0 mt-3 w-96 medical-luxury-card shadow-2xl z-50 max-h-96 overflow-hidden">
                            <!-- Premium Header -->
                            <div class="px-6 py-4 border-b border-gradient-to-r from-redolence-green/20 to-redolence-blue/20 bg-gradient-to-r from-redolence-green/5 to-redolence-blue/5">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-lg font-bold bg-gradient-to-r from-redolence-navy to-redolence-green bg-clip-text text-transparent">Medical Notifications</h3>
                                    <div class="flex items-center space-x-3">
                                        <button onclick="markAllAsRead()" class="text-sm font-semibold text-redolence-blue/70 hover:text-redolence-blue transition-colors duration-300">
                                            Mark all read
                                        </button>
                                        <button onclick="openNotificationsPage()" class="text-sm font-bold text-redolence-green hover:text-redolence-green/80 transition-colors duration-300">
                                            View all
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Premium Category Tabs -->
                            <div class="px-6 py-3 border-b border-gradient-to-r from-redolence-green/10 to-redolence-blue/10 bg-gradient-to-r from-gray-50/50 to-blue-50/30">
                                <div class="flex space-x-2 text-sm">
                                    <button onclick="filterNotifications('all')" class="notification-tab active px-4 py-2 rounded-xl bg-gradient-to-r from-redolence-green to-redolence-blue text-white font-bold shadow-lg transition-all duration-300 hover:scale-105">
                                        All <span id="count-all" class="ml-1 px-2 py-0.5 bg-white/20 rounded-full text-xs">0</span>
                                    </button>
                                    <button onclick="filterNotifications('BOOKING')" class="notification-tab px-4 py-2 rounded-xl text-redolence-navy/70 hover:text-redolence-green hover:bg-redolence-green/10 transition-all duration-300 font-semibold">
                                        Appointments <span id="count-BOOKING" class="ml-1 px-2 py-0.5 bg-redolence-green/10 rounded-full text-xs">0</span>
                                    </button>
                                    <button onclick="filterNotifications('CUSTOMER')" class="notification-tab px-4 py-2 rounded-xl text-redolence-navy/70 hover:text-redolence-blue hover:bg-redolence-blue/10 transition-all duration-300 font-semibold">
                                        Patients <span id="count-CUSTOMER" class="ml-1 px-2 py-0.5 bg-redolence-blue/10 rounded-full text-xs">0</span>
                                    </button>
                                    <button onclick="filterNotifications('SYSTEM')" class="notification-tab px-4 py-2 rounded-xl text-redolence-navy/70 hover:text-redolence-gold hover:bg-redolence-gold/10 transition-all duration-300 font-semibold">
                                        System <span id="count-SYSTEM" class="ml-1 px-2 py-0.5 bg-redolence-gold/10 rounded-full text-xs">0</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Premium Notifications List -->
                            <div id="notificationsList" class="max-h-64 overflow-y-auto">
                                <div class="p-6 text-center text-redolence-navy/60">
                                    <div class="animate-spin rounded-full h-8 w-8 border-b-3 border-gradient-to-r from-redolence-green to-redolence-blue mx-auto mb-3"></div>
                                    <p class="font-semibold">Loading medical notifications...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Luxury User Profile -->
                    <div class="relative">
                        <button id="user-menu-toggle" class="medical-luxury-btn-ghost flex items-center space-x-3 p-3 group">
                            <div class="medical-luxury-avatar group-hover:scale-105 transition-transform duration-300">
                                <?= strtoupper(substr(getCurrentUser()['name'], 0, 2)) ?>
                            </div>
                            <div class="hidden md:block text-left">
                                <p class="text-base font-bold text-redolence-navy group-hover:text-redolence-green transition-colors duration-300"><?= htmlspecialchars(getCurrentUser()['name']) ?></p>
                                <p class="text-sm font-semibold text-redolence-blue/80">Medical Administrator</p>
                            </div>
                            <svg class="medical-luxury-icon group-hover:rotate-180 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>

                        <!-- Luxury User Menu -->
                        <div id="user-menu" class="hidden absolute right-0 mt-3 w-64 medical-luxury-card shadow-2xl z-50">
                            <?php $basePath = getBasePath(); ?>

                            <!-- Premium User Info -->
                            <div class="px-6 py-4 border-b border-gradient-to-r from-redolence-green/20 to-redolence-blue/20 bg-gradient-to-r from-redolence-green/5 to-redolence-blue/5">
                                <div class="flex items-center space-x-4">
                                    <div class="medical-luxury-avatar text-sm">
                                        <?= strtoupper(substr(getCurrentUser()['name'], 0, 2)) ?>
                                    </div>
                                    <div>
                                        <p class="text-base font-bold text-redolence-navy"><?= htmlspecialchars(getCurrentUser()['name']) ?></p>
                                        <p class="text-sm font-semibold text-redolence-blue/80">Medical Administrator</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Premium Menu Items -->
                            <div class="py-2 space-y-1">
                                <a href="<?= $basePath ?>/admin/profile" class="medical-luxury-nav-item mx-2">
                                    <svg class="medical-luxury-icon mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    <span class="font-semibold">Profile Management</span>
                                </a>

                                <a href="<?= $basePath ?>/admin/settings" class="medical-luxury-nav-item mx-2">
                                    <svg class="medical-luxury-icon mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    <span class="font-semibold">System Settings</span>
                                </a>

                                <div class="border-t border-gradient-to-r from-redolence-green/20 to-redolence-blue/20 my-2 mx-4"></div>

                                <a href="<?= $basePath ?>/" class="medical-luxury-nav-item mx-2">
                                    <svg class="medical-luxury-icon mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                    </svg>
                                    <span class="font-semibold">View Public Site</span>
                                </a>

                                <a href="<?= $basePath ?>/auth/logout.php" class="medical-luxury-nav-item mx-2 text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-rose-50 hover:text-red-700 border-red-200 hover:border-red-300">
                                    <svg class="medical-luxury-icon mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                    </svg>
                                    <span class="font-semibold">Secure Logout</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Sidebar Overlay -->
    <div id="mobile-sidebar-overlay" class="hidden fixed inset-0 z-40 lg:hidden">
        <div class="fixed inset-0 bg-black opacity-50"></div>
    </div>

    <!-- Luxury Mobile Medical Sidebar -->
    <div id="mobile-sidebar" class="hidden fixed inset-y-0 left-0 z-50 w-72 medical-luxury-card lg:hidden shadow-2xl">
        <div class="flex items-center justify-between p-6 border-b border-gradient-to-r from-redolence-green/20 to-redolence-blue/20 bg-gradient-to-r from-redolence-green/5 to-redolence-blue/5">
            <h2 class="text-xl font-bold bg-gradient-to-r from-redolence-green to-redolence-blue bg-clip-text text-transparent">Medical Navigation</h2>
            <button id="mobile-sidebar-close" class="medical-luxury-btn-ghost p-3 text-redolence-navy hover:text-redolence-green hover:bg-redolence-green/10 transition-all duration-300">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <nav class="mt-6 px-4">
            <?php include __DIR__ . '/admin_sidebar_nav.php'; ?>
        </nav>
    </div>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-toggle').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.remove('hidden');
            document.getElementById('mobile-sidebar-overlay').classList.remove('hidden');
        });

        document.getElementById('mobile-sidebar-close').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.add('hidden');
            document.getElementById('mobile-sidebar-overlay').classList.add('hidden');
        });

        document.getElementById('mobile-sidebar-overlay').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.add('hidden');
            document.getElementById('mobile-sidebar-overlay').classList.add('hidden');
        });

        // User menu toggle
        document.getElementById('user-menu-toggle').addEventListener('click', function() {
            const menu = document.getElementById('user-menu');
            menu.classList.toggle('hidden');
        });

        // Close user menu when clicking outside
        document.addEventListener('click', function(event) {
            const userMenuToggle = document.getElementById('user-menu-toggle');
            const userMenu = document.getElementById('user-menu');
            
            if (!userMenuToggle.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });
    </script>