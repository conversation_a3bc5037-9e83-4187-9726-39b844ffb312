<?php
/**
 * Admin Booking View
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/staff_schedule_functions.php';
require_once __DIR__ . '/../../includes/room_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Get booking ID from URL
$bookingId = $_GET['id'] ?? '';
if (empty($bookingId)) {
    $_SESSION['error'] = 'Booking ID is required';
    redirect('/admin/bookings');
}

// Handle staff and room assignment
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'assign_staff') {
        $result = assignStaffToBooking($_POST['booking_id'], $_POST['staff_id']);
        if ($result['success']) {
            $_SESSION['success'] = 'Staff member assigned successfully!';
        } else {
            $_SESSION['error'] = $result['error'];
        }
        redirect('/admin/bookings/view.php?id=' . $bookingId);
    } elseif ($_POST['action'] === 'assign_room') {
        $result = assignRoomToBooking($_POST['booking_id'], $_POST['room_id']);
        if ($result['success']) {
            $_SESSION['success'] = isset($result['message']) ? $result['message'] : 'Room assigned successfully!';
        } else {
            $_SESSION['error'] = $result['error'];
            // Add debug information
            error_log("Room assignment failed for booking {$_POST['booking_id']} and room {$_POST['room_id']}: " . $result['error']);
        }
        redirect('/admin/bookings/view.php?id=' . $bookingId);
    } elseif ($_POST['action'] === 'unassign_room') {
        $result = unassignRoomFromBooking($_POST['booking_id']);
        if ($result['success']) {
            $_SESSION['success'] = isset($result['message']) ? $result['message'] : 'Room unassigned successfully!';
        } else {
            $_SESSION['error'] = $result['error'];
        }
        redirect('/admin/bookings/view.php?id=' . $bookingId);
    } elseif ($_POST['action'] === 'confirm_booking') {
        // Validate room assignment before confirmation
        $validation = validateRoomAssignment($_POST['booking_id']);
        if ($validation['success']) {
            $result = updateBookingStatus($_POST['booking_id'], 'CONFIRMED');
            if ($result['success']) {
                $_SESSION['success'] = 'Booking confirmed successfully!';
            } else {
                $_SESSION['error'] = $result['error'];
            }
        } else {
            $_SESSION['error'] = $validation['error'];
        }
        redirect('/admin/bookings/view.php?id=' . $bookingId);
    }
}

// Get booking details
$booking = getBookingById($bookingId);
if (!$booking) {
    $_SESSION['error'] = 'Booking not found';
    redirect('/admin/bookings');
}

// Get available staff for this booking
$availableStaff = getAvailableStaff($booking['date'], $booking['start_time'], $booking['end_time']);

// Get all staff members for assignment
$allStaff = getActiveStaffMembers();

// Get available rooms for this booking
$availableRooms = getAvailableRooms($booking['date'], $booking['start_time'], $booking['end_time'], null, $booking['id']);

// Get current room details if assigned
$currentRoom = null;
if ($booking['room_id']) {
    $currentRoom = getRoomById($booking['room_id']);
}

// Handle messages
$message = '';
$messageType = '';
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    $messageType = 'success';
    unset($_SESSION['success']);
} elseif (isset($_SESSION['error'])) {
    $message = $_SESSION['error'];
    $messageType = 'error';
    unset($_SESSION['error']);
}

$pageTitle = "Booking Details";
include __DIR__ . '/../../includes/admin_header.php';
?>

<!-- Medical Booking Details CSS -->
<style>
/* Medical Booking Details Specific Styles */
.medical-booking-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-booking-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.medical-booking-card:hover::before {
    left: 100%;
}

.medical-booking-card:hover {
    transform: translateY(-2px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 15px 35px var(--shadow-primary);
}

.medical-header-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
}

.medical-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.medical-info-item {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border: 1px solid rgba(73, 167, 92, 0.1);
    border-radius: 12px;
    padding: 1.25rem;
    transition: all 0.3s ease;
}

.medical-info-item:hover {
    transform: translateY(-1px);
    border-color: rgba(73, 167, 92, 0.2);
    box-shadow: 0 8px 20px rgba(73, 167, 92, 0.1);
}

.medical-status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.medical-btn-primary {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.medical-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px var(--shadow-primary);
}

.medical-btn-secondary {
    background: white;
    color: var(--primary-green);
    border: 2px solid var(--primary-green);
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.medical-btn-secondary:hover {
    background: var(--primary-green);
    color: white;
    transform: translateY(-2px);
}

.medical-btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.medical-btn-danger:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);
}

.medical-btn-purple {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.medical-btn-purple:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(139, 92, 246, 0.3);
}

.medical-modal {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
    backdrop-filter: blur(25px);
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 24px;
}

.medical-form-input {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.medical-form-input:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
}

/* Status Badge Styles */
.medical-status-pending {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: white;
}

.medical-status-confirmed {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.medical-status-in_progress {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

.medical-status-completed {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

.medical-status-cancelled {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.medical-status-expired {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
}

@media (max-width: 768px) {
    .medical-info-grid {
        grid-template-columns: 1fr;
    }

    .medical-booking-card {
        border-radius: 16px;
        padding: 1rem;
    }

    .medical-header-card {
        border-radius: 16px;
        padding: 1.5rem;
    }
}
</style>

<div class="min-h-screen admin-page pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Medical Header -->
                    <div class="medical-header-card p-8 mb-8">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-3xl font-bold text-redolence-navy">
                                    Patient Appointment
                                    <span class="text-redolence-green">Details</span>
                                </h1>
                                <p class="mt-2 text-gray-600">Comprehensive appointment management and room assignment</p>
                                <div class="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-green rounded-full mr-2"></div>
                                        ID: <?= substr($booking['id'], 0, 8) ?>...
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 bg-redolence-blue rounded-full mr-2"></div>
                                        <?= date('M j, Y g:i A', strtotime($booking['created_at'])) ?>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0 flex flex-col sm:flex-row gap-3">
                                <a href="<?= getBasePath() ?>/admin/bookings"
                                   class="medical-btn-secondary text-center">
                                    ← Back to Appointments
                                </a>
                                <button onclick="updateStatus('<?= $booking['id'] ?>', '<?= $booking['status'] ?>')"
                                        class="medical-btn-primary">
                                    Update Status
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Message Display -->
                    <?php if ($message): ?>
                        <div class="mb-8 p-6 rounded-2xl border-2 <?= $messageType === 'success' ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 text-green-800' : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200 text-red-800' ?>">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <?php if ($messageType === 'success'): ?>
                                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    <?php else: ?>
                                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-3">
                                    <p class="font-semibold"><?= htmlspecialchars($message) ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
                        <!-- Main Booking Information -->
                        <div class="xl:col-span-2 space-y-8">
                            <!-- Appointment Overview -->
                            <div class="medical-booking-card p-8">
                                <div class="flex items-center justify-between mb-6">
                                    <h2 class="text-2xl font-bold text-redolence-navy">Appointment Overview</h2>
                                    <?php
                                    require_once __DIR__ . '/../../includes/booking_expiration.php';
                                    $statusInfo = getBookingStatusInfo($booking['status']);
                                    $statusClass = 'medical-status-' . strtolower($booking['status']);
                                    ?>
                                    <span class="medical-status-badge <?= $statusClass ?>">
                                        <?= $statusInfo['label'] ?>
                                    </span>
                                </div>

                                <div class="medical-info-grid">
                                    <div class="medical-info-item">
                                        <h3 class="text-sm font-semibold text-redolence-green mb-2 uppercase tracking-wide">Date & Time</h3>
                                        <div class="text-redolence-navy">
                                            <p class="text-xl font-bold"><?= date('M j, Y', strtotime($booking['date'])) ?></p>
                                            <p class="text-gray-600 mt-1"><?= date('g:i A', strtotime($booking['start_time'])) ?> - <?= date('g:i A', strtotime($booking['end_time'])) ?></p>
                                        </div>
                                    </div>

                                    <div class="medical-info-item">
                                        <h3 class="text-sm font-semibold text-redolence-green mb-2 uppercase tracking-wide">Treatment</h3>
                                        <div class="text-redolence-navy">
                                            <?php if (!empty($booking['service_name'])): ?>
                                                <p class="text-lg font-bold"><?= htmlspecialchars($booking['service_name']) ?></p>
                                            <?php elseif (!empty($booking['package_name'])): ?>
                                                <p class="text-lg font-bold"><?= htmlspecialchars($booking['package_name']) ?></p>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-yellow-100 to-yellow-200 text-yellow-800 mt-2">
                                                    PACKAGE
                                                </span>
                                            <?php else: ?>
                                                <p class="text-lg font-bold text-gray-400">Unknown Treatment</p>
                                            <?php endif; ?>
                                            <p class="text-gray-600 mt-1"><?= $booking['service_duration'] ?? 'N/A' ?> minutes</p>
                                        </div>
                                    </div>

                                    <div class="medical-info-item">
                                        <h3 class="text-sm font-semibold text-redolence-green mb-2 uppercase tracking-wide">Specialist</h3>
                                        <div class="flex items-center justify-between">
                                            <div class="text-redolence-navy">
                                                <p class="text-lg font-bold"><?= htmlspecialchars($booking['staff_name'] ?? 'Unassigned') ?></p>
                                                <?php if ($booking['staff_email']): ?>
                                                    <p class="text-gray-600 mt-1"><?= htmlspecialchars($booking['staff_email']) ?></p>
                                                <?php endif; ?>
                                            </div>
                                            <button onclick="openStaffAssignmentModal()"
                                                    class="medical-btn-primary text-sm px-4 py-2">
                                                <?= $booking['staff_name'] ? 'Reassign' : 'Assign' ?>
                                            </button>
                                        </div>
                                    </div>

                                    <div class="medical-info-item">
                                        <h3 class="text-sm font-semibold text-redolence-green mb-2 uppercase tracking-wide">Room Assignment</h3>
                                        <div class="flex items-center justify-between">
                                            <div class="text-redolence-navy">
                                                <?php if ($currentRoom): ?>
                                                    <p class="text-lg font-bold"><?= htmlspecialchars($currentRoom['name']) ?></p>
                                                    <p class="text-gray-600 mt-1"><?= htmlspecialchars($currentRoom['type']) ?> • Capacity: <?= $currentRoom['capacity'] ?></p>
                                                <?php else: ?>
                                                    <p class="text-lg font-bold text-red-500">No Room Assigned</p>
                                                    <p class="text-gray-600 text-sm mt-1">Required before confirmation</p>
                                                <?php endif; ?>
                                            </div>
                                            <div class="flex gap-2">
                                                <button onclick="openRoomAssignmentModal()"
                                                        class="medical-btn-purple text-sm px-4 py-2">
                                                    <?= $currentRoom ? 'Reassign' : 'Assign Room' ?>
                                                </button>
                                                <?php if ($currentRoom): ?>
                                                    <button onclick="unassignRoom('<?= $booking['id'] ?>', '<?= htmlspecialchars($currentRoom['name']) ?>')"
                                                            class="medical-btn-danger text-sm px-4 py-2">
                                                        Unassign
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="medical-info-item">
                                        <h3 class="text-sm font-semibold text-redolence-green mb-2 uppercase tracking-wide">Payment</h3>
                                        <div class="text-redolence-navy">
                                            <p class="text-2xl font-bold text-redolence-green"><?= formatCurrency($booking['total_amount']) ?></p>
                                            <div class="mt-2 space-y-1">
                                                <?php if ($booking['points_used'] > 0): ?>
                                                    <p class="text-sm text-gray-600">Points Used: <span class="font-semibold"><?= $booking['points_used'] ?></span></p>
                                                <?php endif; ?>
                                                <?php if ($booking['points_earned'] > 0): ?>
                                                    <p class="text-sm text-gray-600">Points Earned: <span class="font-semibold"><?= $booking['points_earned'] ?></span></p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                </div>

                                <?php if ($booking['notes']): ?>
                                <div class="medical-info-item mt-6">
                                    <h3 class="text-sm font-semibold text-redolence-green mb-2 uppercase tracking-wide">Special Notes</h3>
                                    <div class="text-redolence-navy bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-200">
                                        <?= nl2br(htmlspecialchars($booking['notes'])) ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Patient Information -->
                        <div class="xl:col-span-1">
                            <div class="medical-booking-card p-8">
                                <h2 class="text-2xl font-bold text-redolence-navy mb-6">Patient Information</h2>

                                <div class="flex items-center mb-6">
                                    <div class="flex-shrink-0 h-16 w-16">
                                        <div class="h-16 w-16 rounded-full bg-gradient-to-br from-redolence-green to-redolence-blue flex items-center justify-center shadow-lg">
                                            <span class="text-xl font-bold text-white">
                                                <?= strtoupper(substr($booking['customer_name'], 0, 2)) ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <h3 class="text-xl font-bold text-redolence-navy"><?= htmlspecialchars($booking['customer_name']) ?></h3>
                                        <p class="text-gray-600 mt-1"><?= htmlspecialchars($booking['customer_email']) ?></p>
                                    </div>
                                </div>

                                <div class="space-y-4">
                                    <?php if ($booking['customer_phone']): ?>
                                    <div class="medical-info-item">
                                        <h4 class="text-sm font-semibold text-redolence-green mb-1 uppercase tracking-wide">Phone</h4>
                                        <p class="text-redolence-navy font-semibold"><?= htmlspecialchars($booking['customer_phone']) ?></p>
                                    </div>
                                    <?php endif; ?>

                                    <div class="medical-info-item">
                                        <h4 class="text-sm font-semibold text-redolence-green mb-1 uppercase tracking-wide">Booking Created</h4>
                                        <p class="text-redolence-navy font-semibold"><?= date('M j, Y g:i A', strtotime($booking['created_at'])) ?></p>
                                    </div>

                                    <?php if ($booking['updated_at'] !== $booking['created_at']): ?>
                                    <div class="medical-info-item">
                                        <h4 class="text-sm font-semibold text-redolence-green mb-1 uppercase tracking-wide">Last Updated</h4>
                                        <p class="text-redolence-navy font-semibold"><?= date('M j, Y g:i A', strtotime($booking['updated_at'])) ?></p>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Medical Status Update Modal -->
<div id="statusModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="medical-modal p-8 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-redolence-navy">Update Status</h2>
            <button onclick="closeStatusModal()" class="text-gray-400 hover:text-redolence-green transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form id="statusForm" method="POST" action="<?= getBasePath() ?>/admin/bookings">
            <input type="hidden" name="action" value="update_status">
            <input type="hidden" name="booking_id" id="statusBookingId">

            <div class="mb-6">
                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">New Status</label>
                <select name="status" id="newStatus" required class="medical-form-input w-full">
                    <option value="PENDING">Pending</option>
                    <option value="CONFIRMED">Confirmed</option>
                    <option value="IN_PROGRESS">In Progress</option>
                    <option value="COMPLETED">Completed</option>
                    <option value="CANCELLED">Cancelled</option>
                    <option value="NO_SHOW">No Show</option>
                    <option value="EXPIRED">Expired</option>
                </select>
            </div>

            <div class="flex gap-4">
                <button type="submit" class="flex-1 medical-btn-primary">
                    Update Status
                </button>
                <button type="button" onclick="closeStatusModal()" class="flex-1 medical-btn-secondary">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Medical Staff Assignment Modal -->
<div id="staffAssignmentModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="medical-modal p-8 w-full max-w-lg mx-4">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-redolence-navy">Assign Specialist</h2>
            <button onclick="closeStaffAssignmentModal()" class="text-gray-400 hover:text-redolence-green transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form id="staffAssignmentForm" method="POST">
            <input type="hidden" name="action" value="assign_staff">
            <input type="hidden" name="booking_id" value="<?= $booking['id'] ?>">

            <div class="mb-6">
                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Select Specialist</label>
                <select name="staff_id" id="staffSelect" required class="medical-form-input w-full">
                    <option value="">Choose a specialist...</option>
                    <?php foreach ($allStaff as $staff): ?>
                        <option value="<?= $staff['id'] ?>" <?= $booking['staff_id'] === $staff['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($staff['name']) ?>
                            <?php
                            $isAvailable = false;
                            foreach ($availableStaff as $available) {
                                if ($available['id'] === $staff['id']) {
                                    $isAvailable = true;
                                    break;
                                }
                            }
                            echo $isAvailable ? ' ✓ Available' : ' ⚠ Busy';
                            ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <?php if (!empty($availableStaff)): ?>
            <div class="mb-6">
                <h4 class="text-sm font-semibold text-green-600 mb-3 uppercase tracking-wide">✓ Available Specialists</h4>
                <div class="space-y-3 max-h-48 overflow-y-auto">
                    <?php foreach ($availableStaff as $staff): ?>
                        <div class="medical-info-item">
                            <p class="text-redolence-navy font-bold"><?= htmlspecialchars($staff['name']) ?></p>
                            <p class="text-gray-600 text-sm"><?= htmlspecialchars($staff['role']) ?></p>
                            <p class="text-gray-500 text-sm">Rate: <?= formatCurrency($staff['hourly_rate']) ?>/hour</p>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php else: ?>
            <div class="mb-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg">
                <p class="text-yellow-800 text-sm">⚠️ No specialists are available for this exact time slot. You can still assign staff, but there may be scheduling conflicts.</p>
            </div>
            <?php endif; ?>

            <div class="flex gap-4">
                <button type="submit" class="flex-1 medical-btn-primary">
                    Assign Specialist
                </button>
                <button type="button" onclick="closeStaffAssignmentModal()" class="flex-1 medical-btn-secondary">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Medical Room Assignment Modal -->
<div id="roomAssignmentModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="medical-modal p-8 w-full max-w-lg mx-4">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-redolence-navy">Assign Treatment Room</h2>
            <button onclick="closeRoomAssignmentModal()" class="text-gray-400 hover:text-redolence-green transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form id="roomAssignmentForm" method="POST">
            <input type="hidden" name="action" value="assign_room">
            <input type="hidden" name="booking_id" value="<?= $booking['id'] ?>">

            <div class="mb-6">
                <label class="block text-sm font-semibold text-redolence-green mb-3 uppercase tracking-wide">Select Room</label>
                <select name="room_id" id="roomSelect" required class="medical-form-input w-full">
                    <option value="">Choose a treatment room...</option>
                    <?php foreach ($availableRooms as $room): ?>
                        <option value="<?= $room['id'] ?>" <?= $booking['room_id'] === $room['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($room['name']) ?> - <?= htmlspecialchars($room['type']) ?>
                            (Capacity: <?= $room['capacity'] ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="mb-6">
                <h4 class="text-sm font-semibold text-redolence-blue mb-3 uppercase tracking-wide">📅 Appointment Details</h4>
                <div class="medical-info-item">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <p class="text-gray-600">Date</p>
                            <p class="text-redolence-navy font-bold"><?= date('M j, Y', strtotime($booking['date'])) ?></p>
                        </div>
                        <div>
                            <p class="text-gray-600">Time</p>
                            <p class="text-redolence-navy font-bold"><?= date('g:i A', strtotime($booking['start_time'])) ?> - <?= date('g:i A', strtotime($booking['end_time'])) ?></p>
                        </div>
                        <div>
                            <p class="text-gray-600">Status</p>
                            <p class="text-redolence-navy font-bold"><?= $booking['status'] ?></p>
                        </div>
                        <?php if ($currentRoom): ?>
                        <div>
                            <p class="text-gray-600">Current Room</p>
                            <p class="text-yellow-600 font-bold"><?= htmlspecialchars($currentRoom['name']) ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <?php if (!empty($availableRooms)): ?>
            <div class="mb-6">
                <h4 class="text-sm font-semibold text-green-600 mb-3 uppercase tracking-wide">✓ Available Rooms (<?= count($availableRooms) ?> found)</h4>
                <div class="space-y-3 max-h-48 overflow-y-auto">
                    <?php foreach ($availableRooms as $room): ?>
                        <div class="medical-info-item">
                            <p class="text-redolence-navy font-bold"><?= htmlspecialchars($room['name']) ?></p>
                            <p class="text-gray-600 text-sm">Type: <?= htmlspecialchars($room['type']) ?></p>
                            <p class="text-gray-500 text-sm">Capacity: <?= $room['capacity'] ?> person<?= $room['capacity'] > 1 ? 's' : '' ?></p>
                            <?php if ($room['description']): ?>
                                <p class="text-gray-400 text-xs mt-1"><?= htmlspecialchars($room['description']) ?></p>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php else: ?>
            <div class="mb-6 p-4 bg-gradient-to-r from-red-50 to-rose-50 border border-red-200 rounded-lg">
                <p class="text-red-800 font-semibold text-sm">⚠️ No rooms are available for this time slot.</p>
                <div class="mt-2 text-red-700 text-xs">
                    <p>This could mean:</p>
                    <ul class="mt-1 ml-4 space-y-1">
                        <li>• All rooms are booked for this time</li>
                        <li>• All rooms are unavailable/under maintenance</li>
                        <li>• There's a conflict with existing bookings</li>
                    </ul>
                </div>
            </div>
            <?php endif; ?>

            <div class="flex gap-4">
                <button type="submit" class="flex-1 medical-btn-primary" <?= empty($availableRooms) ? 'disabled' : '' ?>>
                    Assign Room
                </button>
                <button type="button" onclick="closeRoomAssignmentModal()" class="flex-1 medical-btn-secondary">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Medical Booking Confirmation Section -->
<?php if ($booking['status'] === 'PENDING'): ?>
<div class="fixed bottom-8 right-8 z-40">
    <div class="medical-booking-card p-6 shadow-2xl">
        <h3 class="text-lg font-bold text-redolence-navy mb-4">Ready to Confirm?</h3>

        <?php if (!$booking['room_id']): ?>
            <div class="mb-4 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg">
                <p class="font-semibold text-yellow-800 text-sm">⚠️ Room Assignment Required</p>
                <p class="text-yellow-700 text-xs mt-1">A treatment room must be assigned before confirmation.</p>
            </div>
        <?php endif; ?>

        <form method="POST" onsubmit="return confirmBooking()">
            <input type="hidden" name="action" value="confirm_booking">
            <input type="hidden" name="booking_id" value="<?= $booking['id'] ?>">

            <button type="submit"
                    class="w-full medical-btn-primary text-lg py-3 <?= !$booking['room_id'] ? 'opacity-50 cursor-not-allowed' : '' ?>"
                    <?= !$booking['room_id'] ? 'disabled' : '' ?>>
                ✓ Confirm Appointment
            </button>
        </form>
    </div>
</div>
<?php endif; ?>

<script>
function updateStatus(bookingId, currentStatus) {
    document.getElementById('statusBookingId').value = bookingId;
    document.getElementById('newStatus').value = currentStatus;
    document.getElementById('statusModal').classList.remove('hidden');
}

function closeStatusModal() {
    document.getElementById('statusModal').classList.add('hidden');
}

function openStaffAssignmentModal() {
    document.getElementById('staffAssignmentModal').classList.remove('hidden');
}

function closeStaffAssignmentModal() {
    document.getElementById('staffAssignmentModal').classList.add('hidden');
}

function openRoomAssignmentModal() {
    document.getElementById('roomAssignmentModal').classList.remove('hidden');
    // Optionally refresh available rooms via AJAX
    refreshAvailableRooms();
}

function refreshAvailableRooms() {
    const bookingDate = '<?= $booking['date'] ?>';
    const startTime = '<?= $booking['start_time'] ?>';
    const endTime = '<?= $booking['end_time'] ?>';
    const bookingId = '<?= $booking['id'] ?>';

    console.log('Refreshing available rooms for:', bookingDate, startTime, endTime);

    // You can implement AJAX call here if needed
    // For now, just log the current parameters
}

function closeRoomAssignmentModal() {
    document.getElementById('roomAssignmentModal').classList.add('hidden');
}

function confirmBooking() {
    return confirm('Are you sure you want to confirm this booking? This action will lock the room assignment and notify the customer.');
}

function unassignRoom(bookingId, roomName) {
    if (confirm(`Are you sure you want to unassign "${roomName}" from this booking? The customer may need to be notified if the booking was already confirmed.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="unassign_room">
            <input type="hidden" name="booking_id" value="${bookingId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeStatusModal();
        closeStaffAssignmentModal();
        closeRoomAssignmentModal();
    }
});

// Close modal on backdrop click
document.getElementById('statusModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeStatusModal();
    }
});

document.getElementById('staffAssignmentModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeStaffAssignmentModal();
    }
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
